{"extends": ["stylelint-config-standard", "stylelint-config-standard-scss", "stylelint-config-recommended-vue/scss", "stylelint-config-recess-order"], "overrides": [{"files": ["**/*.{vue,html}"], "customSyntax": "postcss-html"}], "rules": {"selector-class-pattern": ["^([a-z0-9]+(-[a-z0-9]+)*)(__[a-z0-9]+(-[a-z0-9]+)*)?(--[a-z0-9]+(-[a-z0-9]+)*)?$", {"message": "Class selectors should be kebab-case or follow BEM conventions (e.g. block__element--modifier)"}]}}