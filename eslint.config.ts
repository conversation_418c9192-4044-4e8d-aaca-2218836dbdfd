import eslint from '@eslint/js'
import eslintPluginPrettierRecommended from 'eslint-plugin-prettier/recommended'
import eslintPluginVue from 'eslint-plugin-vue'
import globals from 'globals'
import tsEslint from 'typescript-eslint'
import autoImport from './.eslintrc-auto-import.json'
import { fileURLToPath } from 'node:url'
import { includeIgnoreFile } from '@eslint/compat'
import unoCss from '@unocss/eslint-config/flat'

const gitignorePath = fileURLToPath(new URL('.gitignore', import.meta.url))

export default tsEslint.config(
  includeIgnoreFile(gitignorePath),
  eslint.configs.recommended,
  tsEslint.configs.recommended,
  eslintPluginVue.configs['flat/recommended'],
  unoCss,
  {
    languageOptions: {
      globals: {
        ...globals.browser,
        ...autoImport.globals
      }
    }
  },

  {
    files: ['**/*.vue'],
    languageOptions: {
      parserOptions: {
        parser: tsEslint.parser,
        ecmaFeatures: {
          jsx: true
        }
      }
    },
    rules: {
      'vue/multi-word-component-names': 'off'
    }
  },

  {
    files: ['**/*.{ts,tsx,vue}'],
    rules: {}
  },
  eslintPluginPrettierRecommended
)
