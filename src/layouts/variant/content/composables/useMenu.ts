import type { Menu, MenuProps } from 'original-menu'
import type { RouteLocationRaw } from 'vue-router'

const useMenu = () => {
  const router = useRouter()
  const { currentRoute } = useRouter()

  const menuLinkProps = computed(() => (item: Menu) => {
    return resolveMenuLinkProps(item)
  })

  const resolveMenuLinkProps = (item: Menu) => {
    let props: MenuProps = {}

    if (item.type === 'component') {
      props.to = (
        typeof item.to === 'string' ? { name: item.to } : item.to
      ) as RouteLocationRaw
    }

    if (item.type === 'link') {
      props = {
        target: '_blank',
        rel: 'nofollow',
        ...(item.to as MenuProps)
      }
    }

    return props
  }

  const resolveLinkRouteName = (link: Menu) => {
    if (link.type === 'component') {
      if (typeof link.to === 'string') {
        return link.to
      }
      const { name } = router.resolve(link.to as RouteLocationRaw)
      return name
    }
    return null
  }

  const isLinkActive = (link: Menu): boolean => {
    // Matched routes array of current route
    const matchedRoutes = currentRoute.value.matched

    // Check if provided route matches route's matched route
    const resolveRoutedName = resolveLinkRouteName(link)

    if (!resolveRoutedName) return false

    return matchedRoutes.some((route) => {
      return (
        route.name === resolveRoutedName ||
        route.meta.navActiveLink === resolveRoutedName
      )
    })
  }

  const isGroupActive = (children: Menu[]): boolean => {
    return children.some((child) => {
      if (child.type === 'group') {
        return isGroupActive(child.children)
      }
      return isLinkActive(child)
    })
  }

  return {
    menuLinkProps,
    isLinkActive,
    isGroupActive,
    resolveMenuLinkProps
  }
}
export default useMenu
