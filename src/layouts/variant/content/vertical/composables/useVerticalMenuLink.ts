import type { Menu } from 'original-menu'
import useMenu from '@/layouts/variant/content/composables/useMenu'

const useVerticalMenuLink = (item: Menu) => {
  const { isLinkActive, menuLinkProps } = useMenu()
  const { currentRoute } = useRouter()

  // ------------------------------------------------
  // isActive
  // ------------------------------------------------
  const isActive = ref(false)

  // ------------------------------------------------
  // linkProps
  // ------------------------------------------------
  const linkProps = menuLinkProps.value(item)

  // ------------------------------------------------
  // currentRoute
  // ------------------------------------------------
  watch(
    () => currentRoute.value.name,
    () => {
      isActive.value = isLinkActive(item)
    },
    { immediate: true }
  )

  return {
    isActive,
    linkProps
  }
}

export default useVerticalMenuLink
