<script lang="ts" setup>
import { useDisplay } from 'vuetify'

// components
import VerticalMenu from '@/layouts/variant/content/vertical/compontens/VerticalMenu.vue'

defineOptions({
  name: 'LayoutContentVertical'
})

// states
const isVerticalMenuActive = ref(true)

// composables
const { lgAndUp } = useDisplay()
const { isVerticalMenuMini } = storeToRefs(useAppStore())

// methods
const handlerToggleVerticaMenu = () => {
  if (lgAndUp.value) {
    isVerticalMenuMini.value = !isVerticalMenuMini.value
  } else {
    isVerticalMenuActive.value = !isVerticalMenuActive.value
  }
}

// watch
watch(
  () => lgAndUp.value,
  (value) => {
    isVerticalMenuActive.value = value
  },
  {
    immediate: true
  }
)
</script>

<template>
  <v-app class="layout-content-vertical">
    <v-navigation-drawer
      v-model="isVerticalMenuActive"
      class="left-sidebar"
      :expand-on-hover="isVerticalMenuMini"
      floating
      :rail="isVerticalMenuMini"
      rail-width="76"
    >
      <vertical-menu @close-menu="isVerticalMenuActive = false" />
    </v-navigation-drawer>

    <v-app-bar :flat="true">
      <v-app-bar-nav-icon @click="handlerToggleVerticaMenu">
        <i class="i-tabler:menu-2"></i>
      </v-app-bar-nav-icon>
    </v-app-bar>

    <v-main>
      <v-container>
        <slot />
      </v-container>
    </v-main>
  </v-app>
</template>
