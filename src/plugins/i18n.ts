import type { Language } from 'original-config'
import type { PluginInstaller } from 'original-plugin'

import type { App } from 'vue'
import { createI18n } from 'vue-i18n'
import { en, zhHans } from 'vuetify/locale'

const i18n = createI18n({
  legacy: false,
  locale: '',
  messages: {}
})

const localesMap = Object.fromEntries(
  Object.entries(
    import.meta.glob('@/locales/*.json', { import: 'default' })
  ).map(([path, loadLocale]) => [path.match(/([\w-]*)\.json/)?.[1], loadLocale])
) as Record<Language, () => Promise<Record<string, unknown>>>

export const SUPPORT_LOCALES = Object.keys(localesMap)

export const setI18nLanguage = (language: Language) => {
  i18n.global.locale.value = language

  /**
   * NOTE:
   * If you need to specify the language setting for headers, such as the `fetch` API, set it here.
   * The following is an example for axios.
   *
   * axios.defaults.headers.common['Accept-Language'] = language
   */

  if (typeof document !== 'undefined') {
    document.querySelector('html')?.setAttribute('lang', language)
  }
}

const vuetifyLangMap: Record<Language, unknown> = {
  'en-US': en,
  'zh-CN': zhHans
}

export const loadLocaleMessages = async (language: Language) => {
  // load locale messages with dynamic import
  const customMessages = await localesMap[language]()
  const vuetifyMessages = vuetifyLangMap[language] || {}

  const messages = {
    $vuetify: vuetifyMessages,
    $original: customMessages
  }

  i18n.global.setLocaleMessage(language, messages)

  return nextTick()
}

export default i18n

export const install: PluginInstaller = (app: App<Element>) => {
  app.use(i18n)
}
